2025-08-15 00:26:19.446 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig - ⚙️ 时间评估配置:
2025-08-15 00:26:19.447 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⏰ 时间约束: 最大7.0h, 最优6.0h, 可动区间0.5h
2025-08-15 00:26:19.448 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🚗 行驶速度: 40.0km/h (统一速度)
2025-08-15 00:26:19.448 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🎯 决策阈值: 立即停止95.0%, 谨慎85.0%, 最优75.0%
2025-08-15 00:26:19.449 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⚖️ 权重配置: 时间70.0%, 效率20.0%, 均衡10.0%
2025-08-15 00:26:19.449 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    📝 注：配送时间直接使用Accumulation.deliveryTime字段
2025-08-15 00:26:19.449 INFO  c.i.y.p.a.core.TimeBasedTerminationEvaluator - ✅ TimeBasedTerminationEvaluator初始化完成
2025-08-15 00:26:19.449 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：时间评估器状态 = true
2025-08-15 00:26:19.449 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：配置有效性 = true
2025-08-15 00:26:19.462 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - 🗺️ H3GeographicClustering初始化成功（时间评估模式），Uber H3库版本: null
2025-08-15 00:26:19.463 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - ✅ 时间评估器已启用：TimeBasedTerminationEvaluator[最大时间: 7.0h, 可动区间: 0.5h, 速度: 40km/h]
2025-08-15 00:26:19.464 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔧 UnifiedClusteringAdapter初始化完成（使用外部质量评估器）
2025-08-15 00:26:19.464 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - H3六边形网格聚类: ✅ 已加载
2025-08-15 00:26:19.464 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - K-means工作量均衡聚类: ✅ 已加载
2025-08-15 00:26:19.464 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 配置算法类型: H3
2025-08-15 00:26:19.464 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 自动降级: 启用
2025-08-15 00:26:19.464 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 性能对比模式: 禁用
2025-08-15 00:26:19.468 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.468 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.469 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🚀 [手动加载] 开始手动加载OR-Tools原生库
2025-08-15 00:26:19.469 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📦 [提取DLL] 从JAR中提取jniortools.dll
2025-08-15 00:26:19.473 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📍 [Maven仓库] 在Maven仓库中找到: C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-15 00:26:19.473 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎯 [找到JAR] C:\Users\<USER>\.m2\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-15 00:26:19.474 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📄 [DLL信息] jniortools.dll大小: 21976064 字节
2025-08-15 00:26:19.474 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📁 [临时目录] 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************
2025-08-15 00:26:19.591 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [写入完成] 已写入 21976064 字节到 C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.592 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取完成，大小正确: 21976064 字节
2025-08-15 00:26:19.592 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取到: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.592 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [加载DLL] 手动加载DLL文件: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.713 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🧪 [验证加载] 验证OR-Tools原生库是否正确加载
2025-08-15 00:26:19.721 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [验证成功] RoutingIndexManager创建成功，原生库正确加载
2025-08-15 00:26:19.721 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎉 [加载成功] OR-Tools原生库手动加载成功！
2025-08-15 00:26:19.721 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.729 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.745 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:19.745 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:19.745 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:19.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.745 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.746 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.746 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.746 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:26:19.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.747 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.748 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.748 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.748 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:19.748 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:19.748 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:19.752 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:26:19.752 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:26:19.752 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:26:19.754 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.754 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.754 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.754 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.754 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.755 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.755 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.755 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:19.755 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:19.755 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:19.755 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.755 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.755 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.756 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.756 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.757 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:26:19.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.757 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.758 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.758 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.758 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:19.758 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:19.759 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:19.759 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:26:19.759 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:26:19.759 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:26:19.759 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.759 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.759 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.759 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.759 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.760 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.760 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.760 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:19.760 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:19.760 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:19.760 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.760 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.760 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.760 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.760 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.761 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.762 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.762 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:26:19.762 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.762 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.762 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.762 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.762 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.763 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.763 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.763 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:19.763 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:19.763 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:19.763 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:26:19.763 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:26:19.763 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:26:19.768 INFO  c.i.y.p.algorithm.debug.DebugDataExporter - ✅ DebugDataExporter已配置H3时间计算器
2025-08-15 00:26:19.808 INFO  c.i.y.p.a.c.milp.solver.SolverManager - 🔧 已注册 2 个内置求解器
2025-08-15 00:26:19.872 INFO  c.i.y.p.a.c.core.ClusteringPostOptimizerImpl - 🚀 聚类二次优化器初始化完成
2025-08-15 00:26:19.877 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.877 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.877 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.877 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.877 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.878 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.879 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.879 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:19.879 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:19.879 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:19.879 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.879 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.879 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.879 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.879 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.880 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.880 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.880 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:19.880 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:19.880 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:19.880 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.880 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.880 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.880 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.880 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.881 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.881 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.887 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.887 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.887 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.887 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.887 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.888 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.888 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.888 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:19.888 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:19.888 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:19.889 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.889 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.889 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.889 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.889 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.890 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.890 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.890 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:26:19.890 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.890 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.890 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.890 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.890 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.890 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.890 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.890 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:19.890 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:19.890 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:19.890 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:26:19.890 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:26:19.890 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:26:19.894 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.894 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.894 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.894 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.894 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.895 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.896 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.896 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:19.896 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:19.896 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:19.902 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.902 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.902 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.902 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.902 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.903 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.903 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.903 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:19.903 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:19.903 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:19.908 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.908 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.908 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.908 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.908 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.909 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.909 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.909 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:19.909 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:19.909 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:19.909 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.909 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.909 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.909 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.909 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.910 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:26:19.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.910 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.910 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.911 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.912 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.912 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:19.912 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:19.912 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:19.912 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:26:19.912 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:26:19.912 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:26:19.913 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.913 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.913 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.913 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.913 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.914 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.914 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.917 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.917 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.917 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.917 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.917 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.918 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.918 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.918 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:19.918 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:19.918 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:19.918 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:19.918 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:19.918 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:19.918 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:19.918 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:19.919 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:19.919 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:19.919 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:26:19.923 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 开始OR-Tools预初始化...
2025-08-15 00:26:20.432 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 🎉 OR-Tools预初始化成功！
2025-08-15 00:26:20.432 INFO  c.ict.ycwl.pathcalculate.algorithm.core.ORToolsTSP - 🎉 OR-Tools已成功加载并可用！
2025-08-15 00:26:20.435 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:20.435 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:20.435 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:20.435 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:20.435 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:20.436 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:20.436 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:20.436 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:20.436 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:20.436 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:20.436 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:20.436 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:20.436 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:20.436 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:20.438 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:20.438 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:20.438 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:20.439 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:26:20.439 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:20.439 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:20.439 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:20.439 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:20.439 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:20.440 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:20.440 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:20.440 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:20.440 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:20.440 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:20.440 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:26:20.440 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:26:20.440 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:26:20.442 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器启动 - 执行JNI环境预修复
2025-08-15 00:26:20.442 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingModel - 可能错过了修复时机
2025-08-15 00:26:20.442 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingIndexManager - 可能错过了修复时机
2025-08-15 00:26:20.442 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.Loader - 可能错过了修复时机
2025-08-15 00:26:20.442 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ✅ [类加载保护] OR-Tools类尚未加载，环境清洁，执行预防性JNI修复
2025-08-15 00:26:20.444 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - 🔧 开始JNI修复服务...
2025-08-15 00:26:20.642 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - ✅ JNI修复服务完成 - 基础环境已准备就绪，将进行OR-Tools实际测试
2025-08-15 00:26:20.642 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔧 [类加载保护] JNI预修复完成，结果: true
2025-08-15 00:26:20.642 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器初始化完成 - 环境已就绪
2025-08-15 00:26:20.642 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔍 [诊断信息] OR-Tools类加载保护器状态:
2025-08-15 00:26:20.642 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    保护器已初始化: true
2025-08-15 00:26:20.642 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    OR-Tools类已污染: false
2025-08-15 00:26:20.642 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    JNI修复服务状态: JNIFixService[fixed=true, orToolsWorking=false]
2025-08-15 00:26:20.642 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    关键类状态检查:
2025-08-15 00:26:20.642 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.Loader : 已加载
2025-08-15 00:26:20.642 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingIndexManager : 已加载
2025-08-15 00:26:20.642 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingModel : 已加载
2025-08-15 00:26:20.642 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔄 [反射OR-Tools] 开始安全初始化ReflectiveORToolsTSP
2025-08-15 00:26:20.642 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔍 [反射初始化] 开始安全加载OR-Tools类...
2025-08-15 00:26:20.642 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - ✅ [反射初始化] JNI库加载成功
2025-08-15 00:26:20.644 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射初始化] OR-Tools反射功能验证成功！
2025-08-15 00:26:20.644 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射OR-Tools] OR-Tools反射初始化成功！已准备就绪
2025-08-15 00:26:20.647 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:20.647 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:20.647 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:20.647 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:20.647 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:20.649 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:20.649 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:20.649 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:20.649 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:20.649 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:20.649 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 开始全面测试OR-Tools功能...
2025-08-15 00:26:20.658 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - OR-Tools全功能可用
2025-08-15 00:26:20.659 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 🎉 OR-Tools完全可用，支持所有高级功能
2025-08-15 00:26:20.662 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:20.662 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:20.662 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:20.662 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:20.662 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:20.663 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:20.663 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:20.663 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:20.663 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:20.664 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:20.664 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:20.664 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:20.664 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:20.664 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:20.665 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:20.665 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:20.665 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:20.665 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:20.665 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:20.665 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:20.665 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:20.665 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:20.665 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:20.665 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:20.668 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:20.668 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:20.668 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:26:20.668 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:20.668 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:20.668 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:20.668 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:20.668 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:20.669 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:20.669 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:20.670 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:20.670 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:20.670 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:20.670 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:26:20.670 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:26:20.670 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:26:20.672 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:20.672 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:20.672 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:20.672 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:20.672 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:20.674 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:20.674 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:20.674 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:20.674 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:20.674 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:20.674 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:20.674 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:20.674 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:20.674 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:20.674 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:20.675 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:20.675 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:20.675 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:26:20.675 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:20.675 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:20.675 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:20.675 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:20.675 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:20.676 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:20.676 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:20.677 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:20.677 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:20.677 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:20.677 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:26:20.677 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:26:20.677 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:26:20.677 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:20.677 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:20.677 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:20.677 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:20.677 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:20.678 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:20.678 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:20.678 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:20.678 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:20.678 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:20.678 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:20.678 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:20.678 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:20.678 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:20.678 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:20.680 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:20.680 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:20.680 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:26:20.680 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:20.680 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:20.680 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:20.680 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:20.680 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:20.681 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:20.681 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:20.681 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:20.681 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:20.681 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:20.681 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:26:20.681 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:26:20.681 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:26:20.685 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:20.685 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:20.685 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:20.685 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:20.685 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:20.687 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:20.688 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:20.688 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:20.688 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:20.688 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:20.688 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:20.688 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:20.688 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:20.688 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:20.688 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:20.689 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:20.689 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:20.689 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:26:20.689 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:26:20.689 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:26:20.689 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:26:20.689 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:26:20.689 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:26:20.690 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:26:20.690 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:26:20.690 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:26:20.690 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:26:20.690 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:26:20.690 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:26:20.690 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:26:20.690 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
