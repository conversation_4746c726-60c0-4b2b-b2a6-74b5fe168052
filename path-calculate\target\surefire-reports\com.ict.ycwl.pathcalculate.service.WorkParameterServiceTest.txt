-------------------------------------------------------------------------------
Test set: com.ict.ycwl.pathcalculate.service.WorkParameterServiceTest
-------------------------------------------------------------------------------
Tests run: 1, Failures: 0, Errors: 1, Skipped: 0, Time elapsed: 18.784 s <<< FAILURE! - in com.ict.ycwl.pathcalculate.service.WorkParameterServiceTest
testGetAllParameters  Time elapsed: 0.241 s  <<< ERROR!
org.mybatis.spring.MyBatisSystemException: 
nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: com.baomidou.dynamic.datasource.exception.CannotFindDataSourceException: dynamic-datasource can not find primary datasource
### The error may exist in com/ict/ycwl/pathcalculate/mapper/WorkParameterMapper.java (best guess)
### The error may involve com.ict.ycwl.pathcalculate.mapper.WorkParameterMapper.selectList
### The error occurred while executing a query
### Cause: com.baomidou.dynamic.datasource.exception.CannotFindDataSourceException: dynamic-datasource can not find primary datasource
	at com.ict.ycwl.pathcalculate.service.WorkParameterServiceTest.testGetAllParameters(WorkParameterServiceTest.java:28)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 

### Error querying database.  Cause: com.baomidou.dynamic.datasource.exception.CannotFindDataSourceException: dynamic-datasource can not find primary datasource
### The error may exist in com/ict/ycwl/pathcalculate/mapper/WorkParameterMapper.java (best guess)
### The error may involve com.ict.ycwl.pathcalculate.mapper.WorkParameterMapper.selectList
### The error occurred while executing a query
### Cause: com.baomidou.dynamic.datasource.exception.CannotFindDataSourceException: dynamic-datasource can not find primary datasource
	at com.ict.ycwl.pathcalculate.service.WorkParameterServiceTest.testGetAllParameters(WorkParameterServiceTest.java:28)
Caused by: com.baomidou.dynamic.datasource.exception.CannotFindDataSourceException: dynamic-datasource can not find primary datasource
	at com.ict.ycwl.pathcalculate.service.WorkParameterServiceTest.testGetAllParameters(WorkParameterServiceTest.java:28)

