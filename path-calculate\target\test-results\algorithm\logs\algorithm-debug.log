2025-08-15 00:22:39.280 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig - ⚙️ 时间评估配置:
2025-08-15 00:22:39.280 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⏰ 时间约束: 最大7.0h, 最优6.0h, 可动区间0.5h
2025-08-15 00:22:39.281 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🚗 行驶速度: 40.0km/h (统一速度)
2025-08-15 00:22:39.281 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🎯 决策阈值: 立即停止95.0%, 谨慎85.0%, 最优75.0%
2025-08-15 00:22:39.281 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⚖️ 权重配置: 时间70.0%, 效率20.0%, 均衡10.0%
2025-08-15 00:22:39.281 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    📝 注：配送时间直接使用Accumulation.deliveryTime字段
2025-08-15 00:22:39.281 INFO  c.i.y.p.a.core.TimeBasedTerminationEvaluator - ✅ TimeBasedTerminationEvaluator初始化完成
2025-08-15 00:22:39.282 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：时间评估器状态 = true
2025-08-15 00:22:39.282 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：配置有效性 = true
2025-08-15 00:22:39.298 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - 🗺️ H3GeographicClustering初始化成功（时间评估模式），Uber H3库版本: null
2025-08-15 00:22:39.300 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - ✅ 时间评估器已启用：TimeBasedTerminationEvaluator[最大时间: 7.0h, 可动区间: 0.5h, 速度: 40km/h]
2025-08-15 00:22:39.300 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔧 UnifiedClusteringAdapter初始化完成（使用外部质量评估器）
2025-08-15 00:22:39.300 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - H3六边形网格聚类: ✅ 已加载
2025-08-15 00:22:39.300 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - K-means工作量均衡聚类: ✅ 已加载
2025-08-15 00:22:39.300 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 配置算法类型: H3
2025-08-15 00:22:39.300 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 自动降级: 启用
2025-08-15 00:22:39.300 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 性能对比模式: 禁用
2025-08-15 00:22:39.309 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.309 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.311 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🚀 [手动加载] 开始手动加载OR-Tools原生库
2025-08-15 00:22:39.311 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📦 [提取DLL] 从JAR中提取jniortools.dll
2025-08-15 00:22:39.311 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📍 [Classpath] 在classpath中找到: D:\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-15 00:22:39.312 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎯 [找到JAR] D:\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-15 00:22:39.312 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📄 [DLL信息] jniortools.dll大小: 21976064 字节
2025-08-15 00:22:39.313 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📁 [临时目录] 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************
2025-08-15 00:22:39.423 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [写入完成] 已写入 21976064 字节到 C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.425 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取完成，大小正确: 21976064 字节
2025-08-15 00:22:39.426 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取到: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.426 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [加载DLL] 手动加载DLL文件: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.502 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🧪 [验证加载] 验证OR-Tools原生库是否正确加载
2025-08-15 00:22:39.510 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [验证成功] RoutingIndexManager创建成功，原生库正确加载
2025-08-15 00:22:39.511 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎉 [加载成功] OR-Tools原生库手动加载成功！
2025-08-15 00:22:39.511 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.519 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.536 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.536 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.536 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:39.536 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:39.536 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:39.536 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.536 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.536 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.536 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.536 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.538 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.538 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.538 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:39.538 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.538 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.539 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.539 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.539 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.540 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.540 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.540 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:39.540 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:39.540 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:39.544 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:39.544 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:39.544 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:39.545 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.545 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.545 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.545 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.545 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.546 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.546 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.546 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:39.546 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:39.546 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:39.546 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.546 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.546 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.546 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.546 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.547 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.549 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.549 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:39.549 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.549 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.549 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.549 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.549 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.550 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.550 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.550 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:39.550 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:39.550 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:39.550 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:39.550 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:39.550 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:39.550 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.550 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.550 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.550 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.551 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.552 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.552 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.552 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:39.552 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:39.552 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:39.552 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.552 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.552 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.552 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.552 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.553 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.553 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.553 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:39.553 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.553 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.553 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.553 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.554 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.556 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.556 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.556 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:39.556 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:39.556 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:39.556 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:39.556 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:39.556 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:39.564 INFO  c.i.y.p.algorithm.debug.DebugDataExporter - ✅ DebugDataExporter已配置H3时间计算器
2025-08-15 00:22:39.635 DEBUG c.i.y.p.a.c.milp.solver.SolverManager - ✅ 注册求解器: Apache Commons Math v3.6.1
2025-08-15 00:22:39.636 DEBUG c.i.y.p.a.c.milp.solver.SolverManager - ✅ 注册求解器: Builtin Heuristic v1.0.0
2025-08-15 00:22:39.637 INFO  c.i.y.p.a.c.milp.solver.SolverManager - 🔧 已注册 2 个内置求解器
2025-08-15 00:22:39.726 INFO  c.i.y.p.a.c.core.ClusteringPostOptimizerImpl - 🚀 聚类二次优化器初始化完成
2025-08-15 00:22:39.733 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.733 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.733 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.733 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.733 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.734 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.735 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.735 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:39.735 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:39.735 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:39.735 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.735 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.735 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.735 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.735 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.737 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.737 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.737 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:39.737 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:39.737 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:39.737 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.738 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.738 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.738 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.738 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.739 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.739 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.746 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.746 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.746 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.746 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.746 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.748 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.748 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.748 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:39.748 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:39.748 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:39.748 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.748 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.748 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.748 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.748 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.750 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.750 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.750 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:39.750 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.750 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.751 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.751 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.751 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.752 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.752 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.752 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:39.752 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:39.753 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:39.753 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:39.753 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:39.753 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:39.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.757 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.759 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.759 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.759 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:39.759 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:39.759 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:39.773 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.773 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.773 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.773 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.773 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.775 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.775 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.775 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:39.775 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:39.775 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:39.783 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.783 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.783 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.783 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.783 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.784 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.784 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.784 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:39.784 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:39.784 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:39.784 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.785 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.785 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.785 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.785 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.786 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.787 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.787 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:39.787 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.787 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.787 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.787 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.787 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.788 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.788 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.788 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:39.788 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:39.788 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:39.788 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:39.788 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:39.788 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:39.791 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.793 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.793 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.793 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.793 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.794 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.794 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.799 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.799 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.799 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.799 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.799 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.801 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.801 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.801 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:39.801 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:39.801 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:39.801 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:39.801 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:39.801 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:39.801 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:39.801 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:39.803 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:39.803 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:39.803 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:39.808 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 开始OR-Tools预初始化...
2025-08-15 00:22:40.323 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 🎉 OR-Tools预初始化成功！
2025-08-15 00:22:40.323 INFO  c.ict.ycwl.pathcalculate.algorithm.core.ORToolsTSP - 🎉 OR-Tools已成功加载并可用！
2025-08-15 00:22:40.328 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:40.328 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:40.328 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:40.328 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:40.328 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:40.329 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:40.329 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:40.329 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:40.329 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:40.329 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:40.329 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:40.329 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:40.329 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:40.329 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:40.329 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:40.329 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:40.329 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:40.329 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:40.329 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:40.329 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:40.329 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:40.329 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:40.329 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:40.330 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:40.330 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:40.330 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:40.330 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:40.330 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:40.330 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:40.330 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:40.330 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:40.334 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器启动 - 执行JNI环境预修复
2025-08-15 00:22:40.335 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingModel - 可能错过了修复时机
2025-08-15 00:22:40.335 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingIndexManager - 可能错过了修复时机
2025-08-15 00:22:40.335 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.Loader - 可能错过了修复时机
2025-08-15 00:22:40.335 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ✅ [类加载保护] OR-Tools类尚未加载，环境清洁，执行预防性JNI修复
2025-08-15 00:22:40.337 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - 🔧 开始JNI修复服务...
2025-08-15 00:22:40.531 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - ✅ JNI修复服务完成 - 基础环境已准备就绪，将进行OR-Tools实际测试
2025-08-15 00:22:40.531 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔧 [类加载保护] JNI预修复完成，结果: true
2025-08-15 00:22:40.531 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器初始化完成 - 环境已就绪
2025-08-15 00:22:40.531 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔍 [诊断信息] OR-Tools类加载保护器状态:
2025-08-15 00:22:40.531 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    保护器已初始化: true
2025-08-15 00:22:40.531 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    OR-Tools类已污染: false
2025-08-15 00:22:40.531 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    JNI修复服务状态: JNIFixService[fixed=true, orToolsWorking=false]
2025-08-15 00:22:40.531 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    关键类状态检查:
2025-08-15 00:22:40.531 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.Loader : 已加载
2025-08-15 00:22:40.531 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingIndexManager : 已加载
2025-08-15 00:22:40.531 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingModel : 已加载
2025-08-15 00:22:40.531 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔄 [反射OR-Tools] 开始安全初始化ReflectiveORToolsTSP
2025-08-15 00:22:40.531 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔍 [反射初始化] 开始安全加载OR-Tools类...
2025-08-15 00:22:40.531 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - ✅ [反射初始化] JNI库加载成功
2025-08-15 00:22:40.532 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射初始化] OR-Tools反射功能验证成功！
2025-08-15 00:22:40.532 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射OR-Tools] OR-Tools反射初始化成功！已准备就绪
2025-08-15 00:22:40.535 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:40.535 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:40.535 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:40.535 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:40.535 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:40.537 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:40.537 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:40.537 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:40.537 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:40.537 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:40.537 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 开始全面测试OR-Tools功能...
2025-08-15 00:22:40.541 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - OR-Tools全功能可用
2025-08-15 00:22:40.541 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 🎉 OR-Tools完全可用，支持所有高级功能
2025-08-15 00:22:40.543 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:40.544 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:40.544 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:40.544 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:40.544 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:40.545 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:40.546 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:40.546 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:40.546 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:40.548 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:40.548 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:40.548 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:40.548 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:40.548 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:40.550 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:40.550 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:40.550 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:40.550 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:40.550 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:40.550 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:40.550 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:40.550 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:40.550 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:40.550 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:40.551 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:40.551 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:40.551 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:40.551 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:40.551 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:40.551 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:40.551 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:40.551 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:40.553 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:40.553 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:40.553 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:40.553 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:40.553 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:40.553 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:40.553 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:40.553 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:40.555 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:40.555 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:40.555 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:40.555 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:40.555 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:40.556 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:40.556 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:40.556 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:40.556 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:40.556 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:40.556 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:40.556 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:40.556 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:40.556 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:40.556 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:40.558 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:40.558 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:40.558 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:40.558 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:40.558 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:40.558 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:40.558 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:40.558 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:40.559 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:40.559 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:40.559 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:40.559 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:40.559 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:40.559 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:40.559 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:40.559 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:40.559 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:40.559 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:40.559 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:40.559 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:40.559 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:40.561 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:40.561 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:40.561 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:40.561 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:40.561 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:40.561 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:40.561 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:40.561 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:40.561 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:40.561 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:40.562 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:40.562 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:40.562 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:40.562 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:40.562 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:40.562 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:40.562 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:40.562 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:40.564 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:40.564 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:40.564 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:40.564 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:40.564 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:40.564 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:40.564 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:40.564 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:40.568 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:40.568 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:40.568 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:40.568 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:40.568 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:40.570 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:40.570 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:40.570 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:40.570 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:40.570 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:40.570 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:40.570 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:40.570 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:40.570 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:40.570 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:40.571 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:40.571 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:40.571 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:40.571 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:40.571 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:40.571 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:40.571 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:40.571 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:40.572 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:40.572 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:40.572 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:40.572 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:40.572 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:40.572 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:40.572 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:40.572 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:47.950 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig - ⚙️ 时间评估配置:
2025-08-15 00:22:47.950 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⏰ 时间约束: 最大7.0h, 最优6.0h, 可动区间0.5h
2025-08-15 00:22:47.950 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🚗 行驶速度: 40.0km/h (统一速度)
2025-08-15 00:22:47.950 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🎯 决策阈值: 立即停止95.0%, 谨慎85.0%, 最优75.0%
2025-08-15 00:22:47.950 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⚖️ 权重配置: 时间70.0%, 效率20.0%, 均衡10.0%
2025-08-15 00:22:47.950 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    📝 注：配送时间直接使用Accumulation.deliveryTime字段
2025-08-15 00:22:47.950 INFO  c.i.y.p.a.core.TimeBasedTerminationEvaluator - ✅ TimeBasedTerminationEvaluator初始化完成
2025-08-15 00:22:47.950 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：时间评估器状态 = true
2025-08-15 00:22:47.950 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：配置有效性 = true
2025-08-15 00:22:47.950 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - 🗺️ H3GeographicClustering初始化成功（时间评估模式），Uber H3库版本: null
2025-08-15 00:22:47.950 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - ✅ 时间评估器已启用：TimeBasedTerminationEvaluator[最大时间: 7.0h, 可动区间: 0.5h, 速度: 40km/h]
2025-08-15 00:22:47.950 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔧 UnifiedClusteringAdapter初始化完成（使用外部质量评估器）
2025-08-15 00:22:47.950 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - H3六边形网格聚类: ✅ 已加载
2025-08-15 00:22:47.950 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - K-means工作量均衡聚类: ✅ 已加载
2025-08-15 00:22:47.950 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 配置算法类型: H3
2025-08-15 00:22:47.950 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 自动降级: 启用
2025-08-15 00:22:47.950 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 性能对比模式: 禁用
2025-08-15 00:22:47.950 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:47.950 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:47.952 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:47.952 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:47.952 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:47.953 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:47.953 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:47.953 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:47.953 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:47.953 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:47.953 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:47.953 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:47.953 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:47.953 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:47.953 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:47.954 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:47.954 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:47.954 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:47.954 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:47.954 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:47.954 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:47.954 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:47.954 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:47.956 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:47.956 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:47.956 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:47.956 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:47.956 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:47.956 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:47.956 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:47.956 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:47.956 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:47.956 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:47.956 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:47.956 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:47.956 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:47.957 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:47.957 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:47.957 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:47.957 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:47.957 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:47.957 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:47.957 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:47.957 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:47.957 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:47.957 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:47.959 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:47.959 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:47.959 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:47.959 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:47.959 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:47.959 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:47.959 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:47.959 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:47.960 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:47.960 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:47.960 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:47.960 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:47.960 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:47.960 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:47.960 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:47.960 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:47.960 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:47.960 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:47.960 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:47.960 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:47.960 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:47.962 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:47.962 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:47.962 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:47.962 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:47.962 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:47.962 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:47.962 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:47.962 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:47.962 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:47.962 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:47.963 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:47.963 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:47.963 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:47.963 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:47.963 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:47.963 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:47.963 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:47.963 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:47.965 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:47.965 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:47.965 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:47.965 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:47.965 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:47.965 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:47.965 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:47.965 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:47.966 INFO  c.i.y.p.algorithm.debug.DebugDataExporter - ✅ DebugDataExporter已配置H3时间计算器
2025-08-15 00:22:47.997 DEBUG c.i.y.p.a.c.milp.solver.SolverManager - ✅ 注册求解器: Apache Commons Math v3.6.1
2025-08-15 00:22:47.997 DEBUG c.i.y.p.a.c.milp.solver.SolverManager - ✅ 注册求解器: Builtin Heuristic v1.0.0
2025-08-15 00:22:47.997 INFO  c.i.y.p.a.c.milp.solver.SolverManager - 🔧 已注册 2 个内置求解器
2025-08-15 00:22:48.052 INFO  c.i.y.p.a.c.core.ClusteringPostOptimizerImpl - 🚀 聚类二次优化器初始化完成
2025-08-15 00:22:48.053 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.054 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.054 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.054 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.054 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.056 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.057 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.057 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.057 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.057 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.057 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.057 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.057 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.057 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.058 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.061 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.061 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.061 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.061 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.061 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.061 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.061 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.061 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.061 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.061 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.062 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.062 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.066 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.066 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.066 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.066 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.066 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.068 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.068 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.068 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.068 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.068 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.068 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.068 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.068 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.068 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.068 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.070 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.071 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.071 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:48.071 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.071 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.071 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.071 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.071 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.072 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.073 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.073 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.073 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.073 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.073 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:48.073 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:48.073 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:48.076 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.076 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.076 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.076 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.076 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.078 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.078 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.078 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.078 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.078 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.095 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.095 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.095 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.095 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.095 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.097 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.097 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.097 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.097 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.097 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.102 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.103 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.103 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.103 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.103 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.104 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.104 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.104 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.104 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.104 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.104 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.104 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.104 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.104 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.104 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.106 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.106 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.106 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:48.106 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.106 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.106 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.106 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.106 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.108 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.108 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.108 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.108 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.108 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.108 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:48.108 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:48.108 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:48.110 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.110 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.110 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.110 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.110 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.112 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.112 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.115 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.116 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.116 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.116 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.116 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.117 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.117 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.117 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.117 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.118 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.118 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.118 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.118 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.118 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.118 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.119 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.120 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.120 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:48.122 INFO  c.ict.ycwl.pathcalculate.algorithm.core.ORToolsTSP - 🎉 OR-Tools已成功加载并可用！
2025-08-15 00:22:48.125 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.125 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.125 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.125 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.125 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.126 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.126 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.126 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.126 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.126 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.126 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.126 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.126 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.126 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.126 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.127 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.127 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.127 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:48.127 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.127 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.127 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.127 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.127 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.129 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.129 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.129 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.129 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.129 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.130 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:48.130 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:48.130 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:48.132 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔄 [反射OR-Tools] 开始安全初始化ReflectiveORToolsTSP
2025-08-15 00:22:48.132 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔍 [反射初始化] 开始安全加载OR-Tools类...
2025-08-15 00:22:48.132 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - ✅ [反射初始化] JNI库加载成功
2025-08-15 00:22:48.133 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射初始化] OR-Tools反射功能验证成功！
2025-08-15 00:22:48.133 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射OR-Tools] OR-Tools反射初始化成功！已准备就绪
2025-08-15 00:22:48.137 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.137 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.137 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.137 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.137 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.138 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.138 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.138 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.138 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.138 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.138 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 开始全面测试OR-Tools功能...
2025-08-15 00:22:48.141 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - OR-Tools全功能可用
2025-08-15 00:22:48.141 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 🎉 OR-Tools完全可用，支持所有高级功能
2025-08-15 00:22:48.144 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.144 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.144 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.144 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.144 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.145 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.145 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.145 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.145 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.148 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.148 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.148 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.149 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.149 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.149 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.149 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.149 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.149 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.149 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.149 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.149 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.149 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.149 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.149 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.150 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.150 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.150 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:48.150 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.150 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.150 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.152 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.152 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.152 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.152 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.152 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.152 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.152 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.152 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:48.152 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:48.153 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:48.156 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.156 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.156 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.156 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.157 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.158 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.158 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.158 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.158 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.158 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.158 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.158 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.158 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.158 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.158 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.160 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.160 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.160 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:48.160 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.160 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.160 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.160 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.160 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.162 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.162 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.162 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.162 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.162 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.162 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:48.162 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:48.162 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:48.162 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.162 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.162 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.162 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.162 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.163 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.163 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.163 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.163 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.163 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.163 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.163 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.163 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.163 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.163 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.164 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.164 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.164 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:48.164 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.164 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.164 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.164 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.164 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.166 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.166 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.166 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.167 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.167 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.167 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:48.167 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:48.167 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:48.168 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.168 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.168 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.168 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.168 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.170 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.170 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.170 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.170 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.170 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.170 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.170 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.170 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.170 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.170 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.172 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.172 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.172 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:48.172 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:48.172 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:48.172 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:48.172 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:48.172 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:48.174 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:48.174 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:48.174 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:48.174 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:48.174 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:48.174 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:48.174 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:48.174 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig - ⚙️ 时间评估配置:
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⏰ 时间约束: 最大7.0h, 最优6.0h, 可动区间0.5h
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🚗 行驶速度: 40.0km/h (统一速度)
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🎯 决策阈值: 立即停止95.0%, 谨慎85.0%, 最优75.0%
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⚖️ 权重配置: 时间70.0%, 效率20.0%, 均衡10.0%
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    📝 注：配送时间直接使用Accumulation.deliveryTime字段
2025-08-15 00:22:54.260 INFO  c.i.y.p.a.core.TimeBasedTerminationEvaluator - ✅ TimeBasedTerminationEvaluator初始化完成
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：时间评估器状态 = true
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：配置有效性 = true
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - 🗺️ H3GeographicClustering初始化成功（时间评估模式），Uber H3库版本: null
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - ✅ 时间评估器已启用：TimeBasedTerminationEvaluator[最大时间: 7.0h, 可动区间: 0.5h, 速度: 40km/h]
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔧 UnifiedClusteringAdapter初始化完成（使用外部质量评估器）
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - H3六边形网格聚类: ✅ 已加载
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - K-means工作量均衡聚类: ✅ 已加载
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 配置算法类型: H3
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 自动降级: 启用
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 性能对比模式: 禁用
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.260 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.261 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.262 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.262 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.262 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.262 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.262 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.262 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.262 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.262 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.262 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.263 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.263 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.263 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:54.263 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.263 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.263 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.263 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.263 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.265 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.265 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.265 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.265 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.265 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.265 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:54.265 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:54.265 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:54.265 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.265 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.265 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.265 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.265 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.266 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.266 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.266 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.266 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.266 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.266 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.266 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.266 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.266 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.266 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.267 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.267 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.267 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:54.267 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.267 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.267 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.267 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.267 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.269 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.269 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.269 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.269 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.269 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.269 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:54.269 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:54.269 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:54.269 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.269 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.269 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.269 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.269 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.270 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.270 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.270 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.270 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.270 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.270 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.270 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.270 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.270 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.270 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.271 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.271 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.271 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:54.271 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.271 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.271 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.271 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.271 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.273 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.273 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.273 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.273 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.273 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.273 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:54.273 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:54.273 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:54.273 INFO  c.i.y.p.algorithm.debug.DebugDataExporter - ✅ DebugDataExporter已配置H3时间计算器
2025-08-15 00:22:54.302 DEBUG c.i.y.p.a.c.milp.solver.SolverManager - ✅ 注册求解器: Apache Commons Math v3.6.1
2025-08-15 00:22:54.303 DEBUG c.i.y.p.a.c.milp.solver.SolverManager - ✅ 注册求解器: Builtin Heuristic v1.0.0
2025-08-15 00:22:54.303 INFO  c.i.y.p.a.c.milp.solver.SolverManager - 🔧 已注册 2 个内置求解器
2025-08-15 00:22:54.336 INFO  c.i.y.p.a.c.core.ClusteringPostOptimizerImpl - 🚀 聚类二次优化器初始化完成
2025-08-15 00:22:54.338 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.338 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.338 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.338 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.338 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.339 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.339 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.339 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.339 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.339 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.339 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.339 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.339 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.339 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.339 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.340 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.340 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.340 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.340 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.340 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.340 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.340 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.340 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.340 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.340 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.341 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.341 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.345 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.345 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.345 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.345 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.345 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.346 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.346 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.346 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.346 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.346 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.346 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.346 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.346 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.346 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.346 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.347 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.347 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.347 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:54.347 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.347 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.347 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.347 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.347 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.348 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.350 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.350 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.350 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.350 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.350 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:54.350 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:54.350 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:54.351 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.352 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.352 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.352 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.352 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.353 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.353 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.353 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.353 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.353 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.359 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.359 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.359 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.359 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.359 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.360 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.360 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.360 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.360 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.360 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.363 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.363 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.363 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.363 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.363 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.364 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.364 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.364 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.364 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.364 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.364 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.364 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.364 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.364 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.364 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.366 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.366 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.366 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:54.366 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.366 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.366 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.366 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.366 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.367 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.367 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.367 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.367 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.367 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.367 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:54.367 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:54.367 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:54.369 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.369 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.369 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.369 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.369 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.370 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.370 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.374 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.374 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.374 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.374 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.374 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.375 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.375 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.375 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.375 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.375 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.375 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.375 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.375 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.375 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.375 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.377 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.377 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.377 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:54.380 INFO  c.ict.ycwl.pathcalculate.algorithm.core.ORToolsTSP - 🎉 OR-Tools已成功加载并可用！
2025-08-15 00:22:54.381 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.381 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.381 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.381 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.381 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.384 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.384 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.384 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.384 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.384 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.384 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.384 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.384 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.384 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.384 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.385 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.385 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.385 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:54.385 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.385 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.385 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.385 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.385 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.386 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.386 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.386 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.386 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.386 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.386 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:54.386 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:54.386 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:54.388 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔄 [反射OR-Tools] 开始安全初始化ReflectiveORToolsTSP
2025-08-15 00:22:54.388 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔍 [反射初始化] 开始安全加载OR-Tools类...
2025-08-15 00:22:54.388 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - ✅ [反射初始化] JNI库加载成功
2025-08-15 00:22:54.390 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射初始化] OR-Tools反射功能验证成功！
2025-08-15 00:22:54.390 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射OR-Tools] OR-Tools反射初始化成功！已准备就绪
2025-08-15 00:22:54.393 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.393 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.393 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.393 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.393 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.393 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.393 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.393 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.393 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.393 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.393 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 开始全面测试OR-Tools功能...
2025-08-15 00:22:54.395 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - OR-Tools全功能可用
2025-08-15 00:22:54.395 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 🎉 OR-Tools完全可用，支持所有高级功能
2025-08-15 00:22:54.397 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.397 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.397 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.397 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.399 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.399 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.399 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.399 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.399 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.401 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.401 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.401 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.401 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.401 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.403 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.403 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.403 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.403 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.403 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.403 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.403 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.403 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.403 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.403 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.404 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.404 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.404 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:54.404 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.404 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.404 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.404 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.404 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.405 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.405 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.405 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.405 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.405 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.405 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:54.405 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:54.405 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:54.407 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.407 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.407 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.408 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.408 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.409 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.409 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.409 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.409 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.409 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.409 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.409 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.409 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.409 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.409 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.409 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.411 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.411 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:54.411 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.411 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.411 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.411 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.411 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.411 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.412 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.412 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.412 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.412 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.412 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:54.412 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:54.412 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:54.412 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.412 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.412 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.412 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.412 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.413 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.413 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.413 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.413 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.413 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.413 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.413 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.413 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.413 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.413 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.413 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.414 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.414 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:54.414 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.414 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.414 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.414 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.414 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.414 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.414 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.414 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.414 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.414 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.414 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:54.414 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:54.414 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:22:54.417 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.417 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.417 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.417 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.417 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.418 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.418 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.418 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.418 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.418 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.418 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.418 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.418 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.418 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.418 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.419 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.419 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.419 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:22:54.419 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:22:54.419 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:22:54.419 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:22:54.419 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:22:54.419 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:22:54.421 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:22:54.421 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:22:54.421 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:22:54.421 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:22:54.421 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:22:54.421 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:22:54.421 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:22:54.421 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig - ⚙️ 时间评估配置:
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⏰ 时间约束: 最大7.0h, 最优6.0h, 可动区间0.5h
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🚗 行驶速度: 40.0km/h (统一速度)
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🎯 决策阈值: 立即停止95.0%, 谨慎85.0%, 最优75.0%
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⚖️ 权重配置: 时间70.0%, 效率20.0%, 均衡10.0%
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    📝 注：配送时间直接使用Accumulation.deliveryTime字段
2025-08-15 00:23:00.259 INFO  c.i.y.p.a.core.TimeBasedTerminationEvaluator - ✅ TimeBasedTerminationEvaluator初始化完成
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：时间评估器状态 = true
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：配置有效性 = true
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - 🗺️ H3GeographicClustering初始化成功（时间评估模式），Uber H3库版本: null
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - ✅ 时间评估器已启用：TimeBasedTerminationEvaluator[最大时间: 7.0h, 可动区间: 0.5h, 速度: 40km/h]
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔧 UnifiedClusteringAdapter初始化完成（使用外部质量评估器）
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - H3六边形网格聚类: ✅ 已加载
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - K-means工作量均衡聚类: ✅ 已加载
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 配置算法类型: H3
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 自动降级: 启用
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 性能对比模式: 禁用
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.259 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.260 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.260 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.260 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.260 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.260 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.260 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.260 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.260 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.260 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.260 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.262 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.262 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.262 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:23:00.262 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.262 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.262 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.262 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.262 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.264 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.264 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.264 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.264 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.264 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.264 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:23:00.264 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:23:00.264 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:23:00.264 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.264 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.264 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.264 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.264 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.265 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.265 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.265 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.265 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.265 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.265 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.265 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.265 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.265 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.265 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.266 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.266 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.266 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:23:00.266 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.266 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.266 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.266 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.266 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.268 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.268 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.268 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.268 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.268 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.268 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:23:00.268 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:23:00.268 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:23:00.268 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.268 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.268 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.268 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.268 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.269 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.269 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.269 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.269 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.269 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.269 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.270 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.270 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.270 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.270 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.271 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.271 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.271 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:23:00.271 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.271 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.271 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.271 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.271 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.272 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.272 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.272 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.272 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.272 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.272 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:23:00.272 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:23:00.272 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:23:00.272 INFO  c.i.y.p.algorithm.debug.DebugDataExporter - ✅ DebugDataExporter已配置H3时间计算器
2025-08-15 00:23:00.289 DEBUG c.i.y.p.a.c.milp.solver.SolverManager - ✅ 注册求解器: Apache Commons Math v3.6.1
2025-08-15 00:23:00.289 DEBUG c.i.y.p.a.c.milp.solver.SolverManager - ✅ 注册求解器: Builtin Heuristic v1.0.0
2025-08-15 00:23:00.289 INFO  c.i.y.p.a.c.milp.solver.SolverManager - 🔧 已注册 2 个内置求解器
2025-08-15 00:23:00.323 INFO  c.i.y.p.a.c.core.ClusteringPostOptimizerImpl - 🚀 聚类二次优化器初始化完成
2025-08-15 00:23:00.324 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.324 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.324 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.324 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.324 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.325 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.325 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.325 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.325 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.325 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.325 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.325 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.325 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.325 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.325 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.326 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.326 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.326 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.326 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.326 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.326 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.326 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.326 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.326 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.326 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.328 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.328 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.330 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.330 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.330 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.330 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.331 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.331 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.331 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.331 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.331 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.331 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.331 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.331 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.331 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.331 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.331 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.332 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.332 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.332 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:23:00.332 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.333 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.333 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.333 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.333 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.335 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.335 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.335 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.335 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.335 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.335 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:23:00.335 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:23:00.335 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:23:00.336 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.336 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.336 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.336 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.336 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.337 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.338 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.338 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.338 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.338 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.343 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.343 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.343 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.343 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.343 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.344 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.344 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.344 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.344 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.344 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.349 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.349 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.349 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.349 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.349 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.350 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.350 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.350 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.350 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.350 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.350 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.350 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.350 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.350 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.350 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.351 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.351 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.353 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:23:00.353 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.353 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.353 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.353 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.353 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.354 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.354 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.354 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.354 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.354 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.354 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:23:00.354 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:23:00.354 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:23:00.355 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.355 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.355 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.355 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.355 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.357 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.357 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.359 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.359 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.359 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.359 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.359 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.360 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.360 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.360 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.360 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.360 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.360 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.360 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.360 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.360 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.360 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.362 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.362 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.362 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:23:00.365 INFO  c.ict.ycwl.pathcalculate.algorithm.core.ORToolsTSP - 🎉 OR-Tools已成功加载并可用！
2025-08-15 00:23:00.367 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.367 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.367 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.367 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.367 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.369 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.369 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.369 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.369 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.369 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.369 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.369 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.369 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.369 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.369 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.370 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.370 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.370 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:23:00.370 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.370 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.370 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.370 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.370 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.372 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.372 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.372 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.372 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.372 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.372 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:23:00.372 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:23:00.372 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:23:00.374 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔄 [反射OR-Tools] 开始安全初始化ReflectiveORToolsTSP
2025-08-15 00:23:00.374 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔍 [反射初始化] 开始安全加载OR-Tools类...
2025-08-15 00:23:00.374 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - ✅ [反射初始化] JNI库加载成功
2025-08-15 00:23:00.375 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射初始化] OR-Tools反射功能验证成功！
2025-08-15 00:23:00.375 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射OR-Tools] OR-Tools反射初始化成功！已准备就绪
2025-08-15 00:23:00.380 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.380 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.380 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.380 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.380 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.381 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.381 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.381 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.381 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.381 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.381 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 开始全面测试OR-Tools功能...
2025-08-15 00:23:00.385 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - OR-Tools全功能可用
2025-08-15 00:23:00.386 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 🎉 OR-Tools完全可用，支持所有高级功能
2025-08-15 00:23:00.387 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.387 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.387 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.387 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.387 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.390 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.390 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.390 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.390 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.393 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.393 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.393 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.393 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.393 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.394 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.394 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.394 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.394 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.394 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.394 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.394 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.394 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.394 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.394 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.396 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.396 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.396 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:23:00.396 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.396 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.396 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.396 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.396 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.399 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.399 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.399 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.399 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.399 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.399 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:23:00.399 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:23:00.399 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:23:00.400 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.400 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.400 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.401 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.401 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.402 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.402 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.402 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.402 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.402 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.402 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.402 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.402 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.402 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.402 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.405 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.405 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.405 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:23:00.405 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.405 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.405 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.405 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.405 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.406 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.406 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.406 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.406 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.406 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.406 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:23:00.406 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:23:00.406 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:23:00.406 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.406 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.406 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.406 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.406 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.410 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.410 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.410 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.410 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.410 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.410 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.410 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.410 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.410 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.410 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.413 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.414 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.414 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:23:00.414 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.414 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.414 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.414 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.414 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.416 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.416 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.416 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.416 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.416 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.416 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:23:00.416 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:23:00.416 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:23:00.421 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.421 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.421 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.421 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.421 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.423 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.423 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.423 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.423 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.423 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.423 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.423 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.423 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.423 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.423 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.425 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.426 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.426 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:23:00.426 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:23:00.426 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:23:00.427 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:23:00.427 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:23:00.427 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:23:00.432 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:23:00.432 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:23:00.432 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:23:00.432 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:23:00.432 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:23:00.432 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:23:00.432 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:23:00.432 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:24:08.480 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig - ⚙️ 时间评估配置:
2025-08-15 00:24:08.481 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⏰ 时间约束: 最大7.0h, 最优6.0h, 可动区间0.5h
2025-08-15 00:24:08.481 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🚗 行驶速度: 40.0km/h (统一速度)
2025-08-15 00:24:08.481 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    🎯 决策阈值: 立即停止95.0%, 谨慎85.0%, 最优75.0%
2025-08-15 00:24:08.481 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    ⚖️ 权重配置: 时间70.0%, 效率20.0%, 均衡10.0%
2025-08-15 00:24:08.481 INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    📝 注：配送时间直接使用Accumulation.deliveryTime字段
2025-08-15 00:24:08.481 INFO  c.i.y.p.a.core.TimeBasedTerminationEvaluator - ✅ TimeBasedTerminationEvaluator初始化完成
2025-08-15 00:24:08.481 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：时间评估器状态 = true
2025-08-15 00:24:08.481 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔍 UnifiedClusteringAdapter带参构造函数：配置有效性 = true
2025-08-15 00:24:08.501 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - 🗺️ H3GeographicClustering初始化成功（时间评估模式），Uber H3库版本: null
2025-08-15 00:24:08.503 INFO  c.i.y.p.algorithm.core.H3GeographicClustering - ✅ 时间评估器已启用：TimeBasedTerminationEvaluator[最大时间: 7.0h, 可动区间: 0.5h, 速度: 40km/h]
2025-08-15 00:24:08.504 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 🔧 UnifiedClusteringAdapter初始化完成（使用外部质量评估器）
2025-08-15 00:24:08.504 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - H3六边形网格聚类: ✅ 已加载
2025-08-15 00:24:08.504 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - K-means工作量均衡聚类: ✅ 已加载
2025-08-15 00:24:08.504 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 配置算法类型: H3
2025-08-15 00:24:08.504 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 自动降级: 启用
2025-08-15 00:24:08.504 INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 性能对比模式: 禁用
2025-08-15 00:24:08.513 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:08.513 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:08.514 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🚀 [手动加载] 开始手动加载OR-Tools原生库
2025-08-15 00:24:08.514 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📦 [提取DLL] 从JAR中提取jniortools.dll
2025-08-15 00:24:08.514 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📍 [Classpath] 在classpath中找到: D:\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-15 00:24:08.514 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎯 [找到JAR] D:\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-15 00:24:08.515 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📄 [DLL信息] jniortools.dll大小: 21976064 字节
2025-08-15 00:24:08.516 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📁 [临时目录] 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************
2025-08-15 00:24:08.628 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [写入完成] 已写入 21976064 字节到 C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:08.629 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取完成，大小正确: 21976064 字节
2025-08-15 00:24:08.630 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [提取成功] DLL文件提取到: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:08.630 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 📥 [加载DLL] 手动加载DLL文件: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:08.708 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🧪 [验证加载] 验证OR-Tools原生库是否正确加载
2025-08-15 00:24:08.717 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [验证成功] RoutingIndexManager创建成功，原生库正确加载
2025-08-15 00:24:08.718 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 🎉 [加载成功] OR-Tools原生库手动加载成功！
2025-08-15 00:24:08.718 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:08.728 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:08.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:08.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:08.745 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:08.745 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:08.745 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:08.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:08.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:08.745 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:08.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:08.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:08.748 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:08.748 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:08.748 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:24:08.748 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:08.749 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:08.749 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:08.749 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:08.749 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:08.750 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:08.750 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:08.750 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:08.750 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:08.750 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:08.753 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:24:08.755 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:24:08.755 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:24:08.755 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:08.755 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:08.755 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:08.755 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:08.755 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:08.756 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:08.756 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:08.756 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:08.757 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:08.757 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:08.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:08.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:08.757 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:08.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:08.757 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:08.758 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:08.758 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:08.758 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:24:08.758 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:08.758 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:08.758 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:08.758 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:08.759 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:08.760 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:08.760 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:08.760 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:08.760 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:08.760 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:08.760 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:24:08.760 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:24:08.760 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:24:08.760 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:08.760 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:08.760 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:08.760 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:08.760 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:08.762 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:08.762 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:08.762 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:08.762 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:08.762 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:08.762 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:08.762 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:08.762 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:08.762 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:08.762 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:08.763 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:08.763 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:08.763 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:24:08.764 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:08.764 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:08.764 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:08.764 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:08.764 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:08.766 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:08.766 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:08.766 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:08.766 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:08.766 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:08.766 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:24:08.766 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:24:08.766 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:24:08.775 INFO  c.i.y.p.algorithm.debug.DebugDataExporter - ✅ DebugDataExporter已配置H3时间计算器
2025-08-15 00:24:08.852 DEBUG c.i.y.p.a.c.milp.solver.SolverManager - ✅ 注册求解器: Apache Commons Math v3.6.1
2025-08-15 00:24:08.853 DEBUG c.i.y.p.a.c.milp.solver.SolverManager - ✅ 注册求解器: Builtin Heuristic v1.0.0
2025-08-15 00:24:08.854 INFO  c.i.y.p.a.c.milp.solver.SolverManager - 🔧 已注册 2 个内置求解器
2025-08-15 00:24:08.957 INFO  c.i.y.p.a.c.core.ClusteringPostOptimizerImpl - 🚀 聚类二次优化器初始化完成
2025-08-15 00:24:08.965 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:08.965 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:08.965 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:08.965 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:08.965 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:08.967 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:08.968 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:08.968 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:08.968 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:08.968 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:08.968 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:08.968 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:08.968 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:08.968 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:08.968 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:08.970 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:08.970 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:08.970 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:08.970 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:08.970 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:08.970 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:08.970 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:08.970 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:08.970 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:08.970 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:08.972 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:08.972 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:08.981 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:08.981 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:08.981 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:08.981 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:08.981 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:08.983 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:08.983 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:08.984 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:08.984 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:08.984 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:08.984 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:08.984 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:08.984 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:08.984 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:08.984 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:08.986 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:08.986 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:08.986 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:24:08.986 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:08.986 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:08.986 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:08.987 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:08.987 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:08.989 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:08.989 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:08.989 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:08.989 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:08.989 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:08.989 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:24:08.989 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:24:08.989 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:24:08.994 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:08.994 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:08.994 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:08.994 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:08.994 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:08.998 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:08.998 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:08.998 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:08.998 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:08.998 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:09.016 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.017 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.017 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.017 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.017 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.018 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.018 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.018 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:09.018 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:09.018 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:09.029 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.029 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.029 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.029 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.029 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.030 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.030 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.030 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:09.030 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:09.030 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:09.030 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.030 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.030 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.030 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.030 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.031 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.031 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.031 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:24:09.031 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.031 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.031 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.031 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.031 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.033 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.033 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.034 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:09.034 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:09.034 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:09.034 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:24:09.034 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:24:09.034 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:24:09.037 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.037 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.037 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.037 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.037 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.039 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.039 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.045 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.045 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.045 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.045 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.045 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.047 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.047 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.047 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:09.047 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:09.047 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:09.047 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.047 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.047 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.047 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.047 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.049 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.049 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.049 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:24:09.055 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 开始OR-Tools预初始化...
2025-08-15 00:24:09.526 INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 🎉 OR-Tools预初始化成功！
2025-08-15 00:24:09.526 INFO  c.ict.ycwl.pathcalculate.algorithm.core.ORToolsTSP - 🎉 OR-Tools已成功加载并可用！
2025-08-15 00:24:09.529 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.529 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.529 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.529 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.529 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.531 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.531 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.531 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:09.531 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:09.531 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:09.531 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.531 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.531 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.531 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.531 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.532 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.532 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.532 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:24:09.532 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.532 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.532 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.532 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.532 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.533 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.533 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.533 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:09.533 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:09.533 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:09.533 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:24:09.533 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:24:09.533 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:24:09.536 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器启动 - 执行JNI环境预修复
2025-08-15 00:24:09.537 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingModel - 可能错过了修复时机
2025-08-15 00:24:09.537 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.constraintsolver.RoutingIndexManager - 可能错过了修复时机
2025-08-15 00:24:09.537 WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ⚠️ [污染检测] 类已加载但未污染: com.google.ortools.Loader - 可能错过了修复时机
2025-08-15 00:24:09.537 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - ✅ [类加载保护] OR-Tools类尚未加载，环境清洁，执行预防性JNI修复
2025-08-15 00:24:09.537 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - 🔧 开始JNI修复服务...
2025-08-15 00:24:09.715 INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - ✅ JNI修复服务完成 - 基础环境已准备就绪，将进行OR-Tools实际测试
2025-08-15 00:24:09.715 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔧 [类加载保护] JNI预修复完成，结果: true
2025-08-15 00:24:09.716 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🛡️ [类加载保护] OR-Tools类加载保护器初始化完成 - 环境已就绪
2025-08-15 00:24:09.716 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 🔍 [诊断信息] OR-Tools类加载保护器状态:
2025-08-15 00:24:09.716 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    保护器已初始化: true
2025-08-15 00:24:09.716 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    OR-Tools类已污染: false
2025-08-15 00:24:09.716 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    JNI修复服务状态: JNIFixService[fixed=true, orToolsWorking=false]
2025-08-15 00:24:09.716 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    关键类状态检查:
2025-08-15 00:24:09.716 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.Loader : 已加载
2025-08-15 00:24:09.716 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingIndexManager : 已加载
2025-08-15 00:24:09.716 INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingModel : 已加载
2025-08-15 00:24:09.716 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔄 [反射OR-Tools] 开始安全初始化ReflectiveORToolsTSP
2025-08-15 00:24:09.716 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🔍 [反射初始化] 开始安全加载OR-Tools类...
2025-08-15 00:24:09.716 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - ✅ [反射初始化] JNI库加载成功
2025-08-15 00:24:09.716 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射初始化] OR-Tools反射功能验证成功！
2025-08-15 00:24:09.717 INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 🎉 [反射OR-Tools] OR-Tools反射初始化成功！已准备就绪
2025-08-15 00:24:09.720 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.720 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.720 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.720 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.720 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.721 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.721 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.721 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:09.721 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:09.721 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:09.721 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 开始全面测试OR-Tools功能...
2025-08-15 00:24:09.725 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - OR-Tools全功能可用
2025-08-15 00:24:09.727 INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 🎉 OR-Tools完全可用，支持所有高级功能
2025-08-15 00:24:09.728 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.728 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.728 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.728 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.728 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.729 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.729 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.729 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:09.729 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:09.732 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.732 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.732 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.732 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.732 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.733 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.733 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.733 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:09.733 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:09.733 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:09.733 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.733 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.733 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.733 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.733 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.735 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.735 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.735 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:24:09.735 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.735 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.735 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.735 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.735 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.737 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.737 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.737 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:09.737 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:09.737 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:09.737 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:24:09.737 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:24:09.737 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:24:09.740 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.740 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.740 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.740 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.740 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.741 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.741 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.741 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:09.741 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:09.741 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:09.741 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.741 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.741 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.741 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.741 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.742 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.743 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.743 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:24:09.743 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.743 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.743 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.743 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.743 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.745 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:09.745 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:09.745 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:09.745 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:24:09.745 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:24:09.745 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:24:09.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.745 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.745 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.746 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.746 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.746 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:09.746 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:09.746 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:09.746 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.746 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.746 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.746 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.746 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.747 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:24:09.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.747 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.747 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.748 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.748 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.748 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:09.748 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:09.748 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:09.748 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:24:09.749 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:24:09.749 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
2025-08-15 00:24:09.752 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.752 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.752 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.752 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.752 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.753 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.753 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.753 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:09.753 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:09.753 INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 🧮 BranchAndBoundTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:09.753 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.753 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.753 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.753 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.753 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.754 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.754 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.754 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现（自定义fallback）
2025-08-15 00:24:09.754 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🔧 [手动OR-Tools] 开始初始化ManualORToolsTSP
2025-08-15 00:24:09.754 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🚀 [手动初始化] 使用手动加载器初始化OR-Tools
2025-08-15 00:24:09.754 INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - ✅ [手动加载] OR-Tools库已经加载: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-15 00:24:09.754 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 📦 [类加载] 加载OR-Tools Java类
2025-08-15 00:24:09.754 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🧪 [功能测试] 执行基本功能测试
2025-08-15 00:24:09.756 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - ✅ [功能测试] OR-Tools基本功能测试成功
2025-08-15 00:24:09.756 INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 🎉 [手动OR-Tools] OR-Tools手动加载成功！已准备就绪
2025-08-15 00:24:09.756 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🔄 [SafeORToolsTSP] 使用ManualORToolsTSP作为底层实现
2025-08-15 00:24:09.756 INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 🎯 [SafeORToolsTSP] OR-Tools状态: 可用
2025-08-15 00:24:09.756 INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 🧮 DynamicProgrammingTSP初始化 - OR-Tools优先策略启用
2025-08-15 00:24:09.756 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🚀 TSP求解器管理器初始化完成（高性能配置）
2025-08-15 00:24:09.756 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 📦 集成第三方库: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-15 00:24:09.756 INFO  c.i.y.p.algorithm.core.TSPSolverManager - 🎯 算法优先级: OR-Tools > 增强遗传算法 > 基础算法
